import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class AudiencesTwo extends SpriteComponent with HasGameRef<RunChickenRun> {
  AudiencesTwo()
      : super(
          size: Vector2(2000.0, 270.0),
          anchor: Anchor.topLeft,
          // priority: 2,
        );

  var velocity = Vector2(-1, 0).normalized() * 3;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('audiences_2.png');
  }
}
