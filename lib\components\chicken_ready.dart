import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ChickenReady extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation runningAnimation;

  ChickenReady({
    required Vector2 size,
  }) : super(
          size: size,
          anchor: Anchor.center,
          // priority: 3,
        );

  @override
  Future<void> onLoad() async {
    _loadAnimations().then((_) => {animation = runningAnimation});
  }

  Future<void> _loadAnimations() async {
    final runningSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('chicken_ready.png'),
      srcSize: Vector2(23.0, 28.0),
    );

    runningAnimation = runningSpriteSheet.createAnimation(
      row: 0,
      stepTime: 0.1,
      to: 5,
    );
  }

  @override
  void onMount() {
    RunChickenRun.isChickenReadyRemoved = false;
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isChickenReadyRemoved = true;
    super.onRemove();
  }
}
