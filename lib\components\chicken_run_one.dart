import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ChickenRunOne extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation runningAnimation;

  ChickenRunOne()
      : super(
          size: Vector2(44.0, 45.0),
          anchor: Anchor.center,
        );

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    await _loadAnimations();
    animation = runningAnimation;
  }

  Future<void> _loadAnimations() async {
    final runningSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('chicken_run_1.png'),
      srcSize: Vector2(27.0, 29.0), // Correct frame size
    );

    runningAnimation = runningSpriteSheet.createAnimation(
      row: 0,
      stepTime: 0.05, // Much faster animation speed
      to: 2, // 2 frames as specified
      loop: false, // Don't loop the animation
    );
  }

  @override
  void onMount() {
    RunChickenRun.isChickenRunOneAdded = true;
    RunChickenRun.isChickenRunTwoAdded =
        false; // Ensure other chicken is marked as removed
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isChickenRunOneAdded = false;
    super.onRemove();
  }
}
