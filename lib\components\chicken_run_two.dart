import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ChickenRunTwo extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation runningAnimation;

  ChickenRunTwo()
      : super(
          size: Vector2(44.0, 45.0), // Keep display size for consistency
          anchor: Anchor.center,
        );

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    await _loadAnimations();
    animation = runningAnimation;
  }

  Future<void> _loadAnimations() async {
    final runningSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('chicken_run_2.png'),
      srcSize: Vector2(25.0, 28.0), // Updated frame size to match new image
    );

    runningAnimation = runningSpriteSheet.createAnimation(
      row: 0,
      stepTime: 0.05, // Much faster animation speed
      to: 2, // 2 frames as specified
      loop: false, // Don't loop the animation
    );
  }

  @override
  void onMount() {
    RunChickenRun.isChickenRunTwoAdded = true;
    RunChickenRun.isChickenRunOneAdded =
        false; // Ensure other chicken is marked as removed
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isChickenRunTwoAdded = false;
    super.onRemove();
  }
}
