import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ChickenWalk extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation walkingAnimation;

  var velocity = Vector2(1, 0).normalized() * 15;

  ChickenWalk({
    required Vector2 size,
  }) : super(
          size: size,
          anchor: Anchor.center,
          // priority: 3,
        );

  @override
  Future<void> onLoad() async {
    _loadAnimations().then((_) => {animation = walkingAnimation});
  }

  Future<void> _loadAnimations() async {
    final runningSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('chicken_walk.png'),
      srcSize: Vector2(27.0, 28.0),
    );

    walkingAnimation = runningSpriteSheet.createAnimation(
      row: 0,
      stepTime: 0.1,
      to: 6,
    );
  }

  @override
  void onMount() {
    RunChickenRun.isChickenWalkAdded = true;
    super.onMount();
  }

  @override
  void update(double dt) {
    if (RunChickenRun.number == 0) {
      if (position.x < (game.canvasSize.x / 2) + 50) {
        position += velocity * dt;
      } else if (position.x >= (game.canvasSize.x / 2) + 50) {
        RunChickenRun.isWalkFinished = true;
        removeFromParent();
      }
    } else if (RunChickenRun.number <= 99) {
      if (position.x < (game.canvasSize.x / 2) + 50) {
        position += velocity * dt;
      } else if (position.x >= (game.canvasSize.x / 2) + 50) {
        RunChickenRun.isWalkFinished = true;
        removeFromParent();
      }
    } else if (RunChickenRun.number > 99 && RunChickenRun.maxedOut) {
      if (position.x < (game.canvasSize.x / 2) + 50) {
        position += velocity * dt;
      } else if (position.x >= (game.canvasSize.x / 2) + 50) {
        RunChickenRun.isWalkFinished = true;
        removeFromParent();
      }
    }
    super.update(dt);
  }

  @override
  void onRemove() {
    position.x = game.canvasSize.x / 2;
    RunChickenRun.isChickenWalkAdded = false;
    RunChickenRun.isWalkFinished = false;
    super.onRemove();
  }
}
