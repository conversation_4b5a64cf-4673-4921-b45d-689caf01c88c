import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class FalseStart extends SpriteComponent with HasGameRef<RunChickenRun> {
  FalseStart()
      : super(
          size: Vector2(330.0, 225.0),
          anchor: Anchor.center,
        );

  var velocity = Vector2(0, 1).normalized() * 1000;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('false_start.png');
    opacity = 0.4;
  }

  @override
  void update(double dt) {
    if (position.y <= (game.canvasSize.y / 2) - 215) {
      position += velocity * dt;
    } else if (position.y > (game.canvasSize.y / 2) - 215) {
      position.y = (game.canvasSize.y / 2) - 115;
      velocity = Vector2(0, 1).normalized() * 0;
    }

    if (opacity < 1.0) {
      opacity += 0.2;
    }
  }
}
