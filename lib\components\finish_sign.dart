import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class FinishSign extends SpriteComponent with HasGameRef<RunChickenRun> {
  FinishSign()
      : super(
          size: Vector2(550.0, 100.0),
          anchor: Anchor.center,
        );

  late var velocity = Vector2(0, 1).normalized() * 500;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('finished.png');
    opacity = 0.4;
  }

  @override
  void onMount() {
    RunChickenRun.isFinishAdded = true;
    super.onMount();
  }

  @override
  void update(double dt) {
    if (position.y <= (game.canvasSize.y / 2) - 250) {
      position += velocity * dt;
    } else if (position.y > (game.canvasSize.y / 2) - 250) {
      position.y = (game.canvasSize.y / 2) - 250;
      velocity = Vector2(0, 1).normalized() * 0;
    }

    if (opacity < 1.0) {
      opacity += 0.2;
    }
  }

  @override
  void onRemove() {
    velocity = Vector2(0, 1).normalized() * 500;
    position = Vector2(game.canvasSize.x / 2, ((game.canvasSize.y) / 2) - 280);
    RunChickenRun.isFinishAdded = false;
    super.onRemove();
  }
}
