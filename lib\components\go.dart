import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class GoSign extends SpriteComponent with HasGameRef<RunChickenRun> {
  GoSign()
      : super(
          size: Vector2(105.0, 82.0),
          anchor: Anchor.center,
        );

  var velocity = Vector2(0, 1).normalized() * 1000;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('go.png');
    opacity = 0.4;
  }

  @override
  void onMount() {
    RunChickenRun.isGoAdded = true;
    super.onMount();
  }

  @override
  void update(double dt) {
    if (position.y <= (game.canvasSize.y / 2) - 250) {
      position += velocity * dt;
    } else if (position.y > (game.canvasSize.y / 2) - 250) {
      position.y = (game.canvasSize.y / 2) - 250;
      velocity = Vector2(0, 1).normalized() * 0;
    }

    if (opacity < 1.0) {
      opacity += 0.2;
    }
  }

  @override
  void onRemove() {
    position = Vector2(game.canvasSize.x / 2, ((game.canvasSize.y) / 2) - 280);
    velocity = Vector2(0, 1).normalized() * 1000;
    RunChickenRun.isGoAdded = false;
    super.onRemove();
  }
}
