import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class GroundTwo extends SpriteComponent with HasGameRef<RunChickenRun> {
  GroundTwo()
      : super(
          size: Vector2(5610.0, 223.0),
          anchor: Anchor.topLeft,
          // priority: 2,
        );

  var velocity = Vector2(-1, 0).normalized() * 20;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('ground_2.png');
  }
}
