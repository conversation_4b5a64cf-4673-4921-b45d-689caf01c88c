import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class MenuScreen extends PositionComponent with HasGameRef<RunChickenRun>, TapCallbacks {
  late TextComponent _titleText;
  late TextComponent _playButton;
  late TextComponent _settingsButton;
  
  MenuScreen() : super(priority: 1000);
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    
    _titleText = TextComponent(
      text: 'Run Chicken Run',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 48,
          color: Colors.white,
          fontFamily: '04B_19__',
          shadows: [
            Shadow(
              color: Colors.black,
              offset: Offset(2, 2),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 4),
    );
    
    _playButton = TextComponent(
      text: 'Play',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 36,
          color: Colors.white,
          fontFamily: '04B_19__',
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2),
    );
    
    _settingsButton = TextComponent(
      text: 'Settings',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 36,
          color: Colors.white,
          fontFamily: '04B_19__',
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2 + 80),
    );
    
    add(_titleText);
    add(_playButton);
    add(_settingsButton);
  }
  
  @override
  void render(Canvas canvas) {
    // Draw background
    final backgroundPaint = Paint()
      ..color = Colors.black.withAlpha(204) // 0.8 * 255 = 204
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, gameRef.canvasSize.x, gameRef.canvasSize.y),
      backgroundPaint,
    );
    
    // Draw button backgrounds
    final buttonPaint = Paint()
      ..color = Colors.white.withAlpha(51) // 0.2 * 255 = 51
      ..style = PaintingStyle.fill;
    
    // Play button background
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2),
        width: 200,
        height: 60,
      ),
      buttonPaint,
    );
    
    // Settings button background
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2 + 80),
        width: 200,
        height: 60,
      ),
      buttonPaint,
    );
    
    super.render(canvas);
  }
  
  @override
  void onTapDown(TapDownEvent event) {
    final tapPosition = event.canvasPosition;
    
    // Check if play button was tapped
    if (tapPosition.distanceTo(_playButton.position) < 100) {
      gameRef.overlays.remove('Menu');
      gameRef.overlays.add('Game');
      return;
    }
    
    // Check if settings button was tapped
    if (tapPosition.distanceTo(_settingsButton.position) < 100) {
      gameRef.overlays.remove('Menu');
      gameRef.overlays.add('Settings');
      return;
    }
  }
} 