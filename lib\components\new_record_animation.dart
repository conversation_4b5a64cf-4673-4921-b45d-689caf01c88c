import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/overlays/score.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class NewRecordAnimation extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation scoreAnimation;
  static Score score = Score(game: RunChickenRun());

  NewRecordAnimation({
    required Vector2 size,
  }) : super(size: size, anchor: Anchor.center);

  @override
  Future<void> onLoad() async {
    loadAnimations().then((_) => {animation = scoreAnimation});
  }

  Future<void> loadAnimations() async {
    final scoreSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('new_record_animation.png'),
      srcSize: Vector2(600.0, 400.0),
    );

    scoreAnimation = scoreSpriteSheet.createAnimation(
      row: 0,
      to: 9,
      stepTime: 0.05,
    );
  }

  @override
  void onMount() {
    RunChickenRun.isNewRecordAnimationAdded = true;
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isNewRecordAnimationAdded = false;
    super.onRemove();
  }
}
