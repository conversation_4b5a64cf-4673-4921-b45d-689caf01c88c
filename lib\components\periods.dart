import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class Periods extends SpriteComponent with HasGameRef {
  Periods()
      : super(
          size: Vector2(80.0, 32.0),
          anchor: Anchor.center,
        );

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('periods.png');
  }

  @override
  void onMount() {
    RunChickenRun.isPeriodsAdded = true;
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isPeriodsAdded = false;
    super.onRemove();
  }
}
