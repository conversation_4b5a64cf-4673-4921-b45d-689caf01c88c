import 'package:flame/components.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ReadySign extends SpriteComponent with HasGameRef<RunChickenRun> {
  ReadySign()
      : super(
          size: Vector2(258.0, 82.0),
          anchor: Anchor.center,
        );

  var velocity = Vector2(0, 1).normalized() * 1000;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('ready.png');
    opacity = 0.4;
  }

  @override
  void onMount() {
    RunChickenRun.isReadyAdded = true;
    super.onMount();
  }

  @override
  void update(double dt) {
    if (position.y <= (game.canvasSize.y / 2) - 250) {
      position += velocity * dt;
    } else if (position.y > (game.canvasSize.y / 2) - 250) {
      position.y = (game.canvasSize.y / 2) - 250;
      velocity = Vector2(0, 1).normalized() * 0;
    }

    if (opacity < 1.0) {
      opacity += 0.2;
    }
  }

  @override
  void onRemove() {
    position = Vector2(game.canvasSize.x / 2, ((game.canvasSize.y) / 2) - 280);
    velocity = Vector2(0, 1).normalized() * 1000;
    RunChickenRun.isReadyAdded = false;
    super.onRemove();
  }
}
