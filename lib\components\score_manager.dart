import 'package:flame/components.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class ScoreManager extends PositionComponent with HasGameRef<RunChickenRun> {
  static const String _bestScoreKey = 'best_score';
  static const String _lastScoreKey = 'last_score';
  static const String _bestGradeKey = 'best_grade';
  static const String _hasPlayedKey = 'has_played_before';

  double _bestScore = 8.00; // Reset to 8.00
  double _currentScore = 0.0;
  String _bestGrade = 'F';
  bool _hasPlayedBefore = false;

  late TextComponent _bestScoreStroke;
  late TextComponent _bestScoreFill;
  late TextComponent _bestGradeStroke;
  late TextComponent _bestGradeFill;

  ScoreManager() : super(priority: 9999) {
    _loadScores();
  }

  Future<void> _loadScores() async {
    final prefs = await SharedPreferences.getInstance();
    _hasPlayedBefore = prefs.getBool(_hasPlayedKey) ?? false;

    if (_hasPlayedBefore) {
      _bestScore = prefs.getDouble(_bestScoreKey) ?? 8.00;
      _bestGrade = prefs.getString(_bestGradeKey) ?? 'F';
    } else {
      // First time player - no scores to show yet
      _bestScore = 8.00;
      _bestGrade = 'F';
    }

    _currentScore = prefs.getDouble(_lastScoreKey) ?? 0.0;

    // CRITICAL: Synchronize the main game's scoreB4 with the loaded best score
    RunChickenRun.scoreB4 = _bestScore;
    RunChickenRun.scoreBest = _bestScore;
  }

  String _calculateGrade(double score) {
    if (score >= 8.00) return 'F';
    if (score >= 7.60) return 'E';
    if (score >= 7.20) return 'D';
    if (score >= 6.80) return 'C';
    if (score >= 6.40) return 'B';
    if (score >= 6.00) return 'A';
    return 'S';
  }

  Future<void> saveScore(double score) async {
    _currentScore = score;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_lastScoreKey, score);

    // Mark that player has played at least once
    if (!_hasPlayedBefore) {
      _hasPlayedBefore = true;
      await prefs.setBool(_hasPlayedKey, true);
      // First game - this is automatically the best score
      _bestScore = score;
      _bestGrade = _calculateGrade(score);
      await prefs.setDouble(_bestScoreKey, score);
      await prefs.setString(_bestGradeKey, _bestGrade);

      // CRITICAL: Keep main game variables synchronized
      RunChickenRun.scoreB4 = _bestScore;
      RunChickenRun.scoreBest = _bestScore;

      _showScoreDisplays();
    } else if (score < _bestScore) {
      // Subsequent games - only update if better
      _bestScore = score;
      _bestGrade = _calculateGrade(score);
      await prefs.setDouble(_bestScoreKey, score);
      await prefs.setString(_bestGradeKey, _bestGrade);

      // CRITICAL: Keep main game variables synchronized
      RunChickenRun.scoreB4 = _bestScore;
      RunChickenRun.scoreBest = _bestScore;

      updateScoreDisplay();
    }
  }

  double get bestScore => _bestScore;
  double get currentScore => _currentScore;

  bool isNewRecord(double score) {
    return score < _bestScore;
  }

  @override
  Future<void> onLoad() async {
    await super.onLoad();
    await _loadScores(); // Ensure scores are loaded before creating text components

    // Create Best Time stroke component (black outline)
    _bestScoreStroke = TextComponent(
      text: 'Best Time: ${_bestScore.toStringAsPrecision(3)}',
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: 20,
          fontFamily: '04B_19__',
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3
            ..color = Colors.black87,
        ),
      ),
      anchor: Anchor.topLeft,
      position: Vector2(
          -gameRef.canvasSize.x + 10, 10), // Top left corner with small margin
      priority: 100,
    );

    // Create Best Time fill component (white text)
    _bestScoreFill = TextComponent(
      text: 'Best Time: ${_bestScore.toStringAsPrecision(3)}',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 20,
          fontFamily: '04B_19__',
          color: Colors.white,
        ),
      ),
      anchor: Anchor.topLeft,
      position:
          Vector2(-gameRef.canvasSize.x + 10, 10), // Same position as stroke
      priority: 101,
    );

    // Create Best Grade stroke component (black outline)
    _bestGradeStroke = TextComponent(
      text: 'Best Grade: $_bestGrade',
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: 20,
          fontFamily: '04B_19__',
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3
            ..color = Colors.black87,
        ),
      ),
      anchor: Anchor.topRight,
      position: Vector2(-10, 10), // Top right corner with small margin
      priority: 100,
    );

    // Create Best Grade fill component (white text)
    _bestGradeFill = TextComponent(
      text: 'Best Grade: $_bestGrade',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 20,
          fontFamily: '04B_19__',
          color: Colors.white,
        ),
      ),
      anchor: Anchor.topRight,
      position: Vector2(-10, 10), // Same position as stroke
      priority: 101,
    );

    // Only show displays if player has played before
    if (_hasPlayedBefore) {
      add(_bestScoreStroke);
      add(_bestScoreFill);
      add(_bestGradeStroke);
      add(_bestGradeFill);
    }
  }

  void _showScoreDisplays() {
    // Add the displays when player completes their first game
    add(_bestScoreStroke);
    add(_bestScoreFill);
    add(_bestGradeStroke);
    add(_bestGradeFill);
    updateScoreDisplay();
  }

  void updateScoreDisplay() {
    final newTimeText = 'Best Time: ${_bestScore.toStringAsPrecision(3)}';
    _bestScoreStroke.text = newTimeText;
    _bestScoreFill.text = newTimeText;

    final newGradeText = 'Best Grade: $_bestGrade';
    _bestGradeStroke.text = newGradeText;
    _bestGradeFill.text = newGradeText;
  }

  // Temporary method for testing - clears all saved data
  static Future<void> resetAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_bestScoreKey);
    await prefs.remove(_lastScoreKey);
    await prefs.remove(_bestGradeKey);
    await prefs.remove(_hasPlayedKey);
    await prefs.setBool('first_time', true); // Reset TAP TAP instruction too
  }

  // Method to reload data and hide displays if needed
  Future<void> reloadData() async {
    await _loadScores();

    // Remove existing displays
    if (children.contains(_bestScoreStroke)) {
      remove(_bestScoreStroke);
      remove(_bestScoreFill);
      remove(_bestGradeStroke);
      remove(_bestGradeFill);
    }

    // Only re-add if player has played before
    if (_hasPlayedBefore) {
      add(_bestScoreStroke);
      add(_bestScoreFill);
      add(_bestGradeStroke);
      add(_bestGradeFill);
      updateScoreDisplay();
    }

    // CRITICAL: Ensure synchronization after reload
    RunChickenRun.scoreB4 = _bestScore;
    RunChickenRun.scoreBest = _bestScore;
  }
}
