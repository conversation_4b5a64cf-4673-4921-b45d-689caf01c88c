import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/run_chicken_run.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends PositionComponent with HasGameRef<RunChickenRun>, TapCallbacks {
  late TextComponent _titleText;
  late TextComponent _backButton;
  late TextComponent _soundToggle;
  late TextComponent _vibrationToggle;
  
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  
  SettingsScreen() : super(priority: 1000);
  
  @override
  Future<void> onLoad() async {
    await super.onLoad();
    await _loadSettings();
    
    _titleText = TextComponent(
      text: 'Settings',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 48,
          color: Colors.white,
          fontFamily: '04B_19__',
          shadows: [
            Shadow(
              color: Colors.black,
              offset: Offset(2, 2),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 4),
    );
    
    _backButton = TextComponent(
      text: 'Back',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 36,
          color: Colors.white,
          fontFamily: '04B_19__',
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y - 100),
    );
    
    _soundToggle = TextComponent(
      text: 'Sound: ON',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 36,
          color: Colors.white,
          fontFamily: '04B_19__',
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2),
    );
    
    _vibrationToggle = TextComponent(
      text: 'Vibration: ON',
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 36,
          color: Colors.white,
          fontFamily: '04B_19__',
        ),
      ),
      anchor: Anchor.center,
      position: Vector2(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2 + 80),
    );
    
    add(_titleText);
    add(_backButton);
    add(_soundToggle);
    add(_vibrationToggle);
  }
  
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _soundEnabled = prefs.getBool('sound_enabled') ?? true;
    _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
  }
  
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);
  }
  
  @override
  void render(Canvas canvas) {
    // Draw background
    final backgroundPaint = Paint()
      ..color = Colors.black.withAlpha(204) // 0.8 * 255 = 204
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, gameRef.canvasSize.x, gameRef.canvasSize.y),
      backgroundPaint,
    );
    
    // Draw button backgrounds
    final buttonPaint = Paint()
      ..color = Colors.white.withAlpha(51) // 0.2 * 255 = 51
      ..style = PaintingStyle.fill;
    
    // Back button background
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(gameRef.canvasSize.x / 2, gameRef.canvasSize.y - 100),
        width: 200,
        height: 60,
      ),
      buttonPaint,
    );
    
    // Sound toggle background
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2),
        width: 200,
        height: 60,
      ),
      buttonPaint,
    );
    
    // Vibration toggle background
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(gameRef.canvasSize.x / 2, gameRef.canvasSize.y / 2 + 80),
        width: 200,
        height: 60,
      ),
      buttonPaint,
    );
    
    super.render(canvas);
  }
  
  @override
  void onTapDown(TapDownEvent event) {
    final tapPosition = event.canvasPosition;
    
    // Check if back button was tapped
    if (tapPosition.distanceTo(_backButton.position) < 100) {
      gameRef.overlays.remove('Settings');
      gameRef.overlays.add('Menu');
      return;
    }
    
    // Check if sound toggle was tapped
    if (tapPosition.distanceTo(_soundToggle.position) < 100) {
      _soundEnabled = !_soundEnabled;
      _soundToggle.text = 'Sound: ${_soundEnabled ? 'ON' : 'OFF'}';
      _saveSettings();
      return;
    }
    
    // Check if vibration toggle was tapped
    if (tapPosition.distanceTo(_vibrationToggle.position) < 100) {
      _vibrationEnabled = !_vibrationEnabled;
      _vibrationToggle.text = 'Vibration: ${_vibrationEnabled ? 'ON' : 'OFF'}';
      _saveSettings();
      return;
    }
  }
} 