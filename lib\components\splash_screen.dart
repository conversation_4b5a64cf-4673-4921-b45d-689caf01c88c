import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SplashScreen extends StatefulWidget {
  final VoidCallback onSplashComplete;

  const SplashScreen({
    super.key,
    required this.onSplashComplete,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _slideController;

  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Create animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    // Start the splash sequence
    _startSplashSequence();
  }

  void _startSplashSequence() async {
    // Add a small delay before starting animations
    await Future.delayed(const Duration(milliseconds: 300));

    // Start animations in sequence
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _slideController.forward();

    // Wait for animations to complete and show splash for a bit
    await Future.delayed(const Duration(milliseconds: 2000));

    // Fade out and complete
    await _fadeController.reverse();

    // Trigger haptic feedback
    HapticFeedback.lightImpact();

    // Call completion callback
    widget.onSplashComplete();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // Clean white background
      body: AnimatedBuilder(
        animation: Listenable.merge(
            [_fadeController, _scaleController, _slideController]),
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Your brand logo - clean and prominent
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Column(
                      children: [
                        // Brand logo - clean and prominent
                        Container(
                          width: 300,
                          height: 300,
                          padding: EdgeInsets.zero,
                          margin: EdgeInsets.zero,
                          child: Image.asset(
                            'assets/images/glanced.png',
                            width: 300,
                            height: 300,
                            fit: BoxFit.contain,
                          ),
                        ),
                        // "presents" text - elegant and clean
                        Stack(
                          children: [
                            // Stroke
                            Text(
                              'presents',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                fontFamily: '04B_19__',
                                letterSpacing: 1.5,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 3
                                  ..color = Colors.black87,
                              ),
                            ),
                            // Fill
                            const Text(
                              'presents',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.lightBlue,
                                fontFamily: '04B_19__',
                                letterSpacing: 1.5,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        // Game title - bold and impactful
                        Stack(
                          children: [
                            // Stroke
                            Text(
                              'RUN CHICKEN RUN',
                              style: TextStyle(
                                fontSize: 36,
                                fontWeight: FontWeight.w900,
                                fontFamily: '04B_19__',
                                letterSpacing: 1.5,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 5
                                  ..color = Colors.black87,
                              ),
                            ),
                            // Fill
                            const Text(
                              'RUN CHICKEN RUN',
                              style: TextStyle(
                                fontSize: 36,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                                fontFamily: '04B_19__',
                                letterSpacing: 1.5,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 15),

                  // Subtitle with slide animation
                  SlideTransition(
                    position: _slideAnimation,
                    child: Stack(
                      children: [
                        // Stroke
                        Text(
                          'Tap Fast, Run Faster!',
                          style: TextStyle(
                            fontSize: 18,
                            fontFamily: '04B_19__',
                            letterSpacing: 1,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 3
                              ..color = Colors.black87,
                          ),
                        ),
                        // Fill
                        const Text(
                          'Tap Fast, Run Faster!',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.amber,
                            fontFamily: '04B_19__',
                            letterSpacing: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
