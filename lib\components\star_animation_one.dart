import 'package:flame/components.dart';
import 'package:flame/sprite.dart';
import 'package:run_chicken_run/overlays/score.dart';
import 'package:run_chicken_run/run_chicken_run.dart';

class StarAnimationOne extends SpriteAnimationComponent
    with HasGameRef<RunChickenRun> {
  static late final SpriteAnimation starAnimation;
  static Score score = Score(game: RunChickenRun());

  StarAnimationOne({
    required Vector2 size,
  }) : super(size: size, anchor: Anchor.center);

  @override
  Future<void> onLoad() async {
    loadAnimations().then((_) => {animation = starAnimation});
  }

  Future<void> loadAnimations() async {
    final scoreSpriteSheet = SpriteSheet(
      image: await gameRef.images.load('star_animation.png'),
      srcSize: Vector2(32.0, 32.0),
    );

    starAnimation = scoreSpriteSheet.createAnimation(
      row: 0,
      to: 13,
      stepTime: 0.05,
    );
  }

  @override
  void onMount() {
    RunChickenRun.isStarAnimationOneAdded = true;
    super.onMount();
  }

  @override
  void onRemove() {
    RunChickenRun.isStarAnimationOneAdded = false;
    super.onRemove();
  }
}
