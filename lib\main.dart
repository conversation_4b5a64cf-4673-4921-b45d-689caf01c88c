import 'package:flame/flame.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/splash_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Optimize image rendering for pixel art
  Flame.images.clearCache();

  // Set device orientation and fullscreen
  await Flame.device.fullScreen();
  await Flame.device.setPortrait();

  runApp(const SplashApp());
}
