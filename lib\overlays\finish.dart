import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:typed_data';
import '../splash_app.dart';

import '../run_chicken_run.dart';
import 'package:animated_button/animated_button.dart';

class Finish extends StatelessWidget {
  final RunChickenRun game;

  const Finish({super.key, required this.game});

  // Share functionality with instant response
  void _shareScreenshot() {
    // Play button press sound immediately for instant feedback
    RunChickenRun.playSound('pressing_button.wav');

    // Capture and share immediately without additional async wrappers
    _performShare();
  }

  Future<void> _performShare() async {
    try {
      // Capture screenshot as quickly as possible
      final Uint8List? image = await widgetsToImageController.capture();
      if (image != null) {
        String shareText;
        String fileName;

        // Determine the appropriate message based on the scenario
        if (RunChickenRun.isNewRecord) {
          // New record achieved
          if (RunChickenRun.scoreNow < 6.00) {
            // S-grade new record
            shareText =
                'LEGENDARY S-GRADE achieved in Run Chicken Run! 🏆🐔⚡ Under 6 seconds - I\'m unstoppable! 💨 #RunChickenRun #SGrade #NewRecord';
            fileName = 'run_chicken_run_s_grade.png';
          } else {
            // Regular new record
            shareText =
                'I just set a new personal record in Run Chicken Run! 🐔🏃‍♂️💨 Getting faster every time! 🏆 #RunChickenRun #NewRecord';
            fileName = 'run_chicken_run_new_record.png';
          }
        } else if (RunChickenRun.scoreNow >= 8.00) {
          // Timeout scenario
          shareText =
              'Time ran out in Run Chicken Run! ⏰🐔 Sometimes the pressure gets to you! 😅 Practice makes perfect! 💪 #RunChickenRun #TimeOut';
          fileName = 'run_chicken_run_timeout.png';
        } else {
          // Finished but no new record
          shareText =
              'Just finished a run in Run Chicken Run! 🐔🏃‍♂️ ${RunChickenRun.scoreNow.toStringAsFixed(2)} seconds - not bad! 😊 #RunChickenRun #ChickenRun';
          fileName = 'run_chicken_run_finish.png';
        }

        // Share immediately after capture
        await Share.shareXFiles(
          [
            XFile.fromData(
              image,
              name: fileName,
              mimeType: 'image/png',
            )
          ],
          text: shareText,
        );
      }
    } catch (e) {
      // Handle error silently - don't disrupt gameplay
      // Debug print removed
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        padding: const EdgeInsets.all(55),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: AnimatedButton(
                onPressed: () async {
                  // Play button press sound
                  RunChickenRun.playSound('pressing_button.wav');

                  // Stop all sounds including audience reactions
                  RunChickenRun.stopAllSounds();

                  // Stop background music when restarting game
                  RunChickenRun.stopBackgroundMusic();

                  // Stop continuous confetti if active
                  game.stopContinuousConfetti();

                  // Reset S-grade flags
                  RunChickenRun.isSGradeAchieved = false;
                  RunChickenRun.continuousConfettiActive = false;

                  RunChickenRun.finished = false;
                  RunChickenRun.maxedOut = false;
                  RunChickenRun.canRun = true;
                  RunChickenRun.number = 100;
                  RunChickenRun.ground2.position.x = 0;
                  RunChickenRun.marks.position.x = (game.canvasSize.x / 2) + 2;
                  RunChickenRun.audiences2.position.x = 0;
                  if (RunChickenRun.isFinishAdded) {
                    game.remove(RunChickenRun.finishSign);
                  }
                  if (RunChickenRun.isFailAdded) {
                    game.remove(RunChickenRun.failSign);
                  }
                  if (RunChickenRun.isScoreBoardAdded) {
                    game.remove(RunChickenRun.scoreBoard);
                  }
                  game.overlays.remove('Score');
                  game.overlays.remove('Finish');
                  if (RunChickenRun.isNewRecordAnimationAdded) {
                    game.remove(RunChickenRun.newRecordAnimation);
                  }
                  if (RunChickenRun.isStarAnimationOneAdded) {
                    game.remove(RunChickenRun.starAnimationOne);
                  }
                  if (RunChickenRun.isStarAnimationTwoAdded) {
                    game.remove(RunChickenRun.starAnimationTwo);
                  }
                  game.add(RunChickenRun.oneHundred);
                  game.add(RunChickenRun.runTimerDisplay);
                  if (RunChickenRun.isChickenWalkAdded) {
                    game.remove(RunChickenRun.chickenWalk);
                  }
                  if (RunChickenRun.isChickenReadyRemoved == false) {
                    RunChickenRun.chickenReady.position =
                        Vector2(game.canvasSize.x / 2, game.canvasSize.y - 234);
                  } else if (RunChickenRun.isChickenReadyRemoved) {
                    RunChickenRun.chickenReady.position =
                        Vector2(game.canvasSize.x / 2, game.canvasSize.y - 234);
                    game.add(RunChickenRun.chickenReady);
                  }

                  if (RunChickenRun.isPeriodsAdded == false &&
                      RunChickenRun.canRun) {
                    game.add(RunChickenRun.periods);
                  }
                  await Future.delayed(const Duration(milliseconds: 584));
                  if (RunChickenRun.isPeriodsAdded && RunChickenRun.canRun) {
                    game.remove(RunChickenRun.periods);
                  }
                  if (RunChickenRun.isReadyAdded == false &&
                      RunChickenRun.canRun) {
                    // Re-enable audience reactions for new game
                    RunChickenRun.audienceReactionsEnabled = true;

                    // Start the ready-set-go sequence and play Ready sound immediately
                    RunChickenRun.startReadySetGoSequence();
                    RunChickenRun.playReadySound();
                    game.add(RunChickenRun.readySign);
                    RunChickenRun.runTimerStartSignal.timer.start();
                  }
                  await Future.delayed(const Duration(milliseconds: 583));
                  if (RunChickenRun.isReadyAdded && RunChickenRun.canRun) {
                    game.remove(RunChickenRun.readySign);
                  }
                  if (RunChickenRun.isSetAdded == false &&
                      RunChickenRun.canRun) {
                    game.add(RunChickenRun.setSign);
                    // Play Set sound
                    RunChickenRun.playSetSound();
                  }
                  await Future.delayed(const Duration(milliseconds: 583));
                  if (RunChickenRun.isSetAdded && RunChickenRun.canRun) {
                    game.remove(RunChickenRun.setSign);
                  }
                  if (RunChickenRun.isGoAdded == false &&
                      RunChickenRun.canRun) {
                    game.add(RunChickenRun.goSign);
                    // Play Go sound
                    RunChickenRun.playGoSound();
                    RunChickenRun.canTap = true;

                    // Start persistent reminder timer for player engagement on restart
                    RunChickenRun.persistentTapReminderTimer.timer.start();
                  }
                },
                width: 100,
                height: 65,
                color: Colors.amber,
                duration: 50,
                child: const Icon(
                  Icons.replay_circle_filled_rounded,
                  size: 43.0,
                  color: Colors.deepOrange,
                ),
              ),
            ),
            // Share button for all scenarios (positioned on the right)
            Align(
              alignment: Alignment.bottomCenter,
              child: AnimatedButton(
                onPressed: _shareScreenshot,
                width: 100,
                height: 65,
                color: Colors.amber,
                duration: 50,
                child: const Icon(
                  Icons.share,
                  size: 43.0,
                  color: Colors.deepOrange,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
