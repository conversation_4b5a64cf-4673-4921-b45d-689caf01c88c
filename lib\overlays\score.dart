import 'package:countup/countup.dart';
import 'package:flutter/material.dart';
import '../run_chicken_run.dart';

class Score extends StatelessWidget {
  final RunChickenRun game;

  const Score({super.key, required this.game});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          right: (game.canvasSize.x / 2) - 13,
          top: (game.canvasSize.y / 2) - 160,
          child: Container(
            padding: const EdgeInsets.all(55),
            child: Countup(
              begin: 0.00,
              end: RunChickenRun.scoreNow,
              precision: 2,
              duration: const Duration(seconds: 1),
              separator: ',',
              style: TextStyle(
                fontSize: 36,
                fontFamily: '04B_19__',
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = 7
                  ..color = Colors.black87,
              ),
            ),
          ),
        ),
        Positioned(
          right: (game.canvasSize.x / 2) - 13,
          top: (game.canvasSize.y / 2) - 160,
          child: Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(55),
            child: Countup(
              begin: 0.00,
              end: RunChickenRun.scoreNow,
              precision: 2,
              duration: const Duration(seconds: 1),
              separator: ',',
              style: const TextStyle(
                fontSize: 36,
                fontFamily: '04B_19__',
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
