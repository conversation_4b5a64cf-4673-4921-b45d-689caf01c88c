import 'package:flame/components.dart';
import 'package:flame/game.dart';
import 'package:flame/input.dart';
import 'package:flame/events.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flame_audio/flame_audio.dart';
import 'dart:math' as math;
import 'package:run_chicken_run/components/audiences_two.dart';
import 'package:run_chicken_run/components/audiences_one.dart';
import 'package:run_chicken_run/components/chicken_ready.dart';
import 'package:run_chicken_run/components/chicken_run_one.dart';
import 'package:run_chicken_run/components/chicken_run_two.dart';
import 'package:run_chicken_run/components/chicken_walk.dart';
import 'package:run_chicken_run/components/fail_sign.dart';
import 'package:run_chicken_run/components/false_start.dart';
import 'package:run_chicken_run/components/finish_sign.dart';
import 'package:run_chicken_run/components/game_title.dart';
import 'package:run_chicken_run/components/go.dart';
import 'package:run_chicken_run/components/ground_one.dart';
import 'package:run_chicken_run/components/ground_two.dart';
import 'package:run_chicken_run/components/marks.dart';
import 'package:run_chicken_run/components/new_record_animation.dart';
import 'package:run_chicken_run/components/periods.dart';
import 'package:run_chicken_run/components/ready.dart';
import 'package:run_chicken_run/components/scoreboard.dart';
import 'package:run_chicken_run/components/set.dart';
import 'package:run_chicken_run/components/star_animation_one.dart';
import 'package:run_chicken_run/components/star_animation_two.dart';
import 'package:run_chicken_run/components/arena.dart';
import 'package:run_chicken_run/components/score_manager.dart';
import 'package:run_chicken_run/overlays/finish.dart';
import 'package:run_chicken_run/overlays/score.dart';
import 'package:vibration/vibration.dart';

class VisibleDustParticle extends CircleComponent {
  Vector2 velocity;
  double _elapsed = 0;
  double lifespan = 2.0; // Longer lifespan to see them
  final _random = math.Random();
  late Color _originalColor;

  VisibleDustParticle({
    required Vector2 startPosition,
    required this.velocity,
  }) : super(
          position: startPosition,
          radius: 1 +
              math.Random().nextDouble() * 2, // Much smaller: 1-3 pixels radius
          priority: 1500, // High priority to render on top
        ) {
    // Realistic dust colors - browns and tans
    _originalColor = Color.lerp(
      const Color(0xFF8B4513), // Saddle brown
      const Color(0xFFD2B48C), // Tan
      _random.nextDouble(),
    )!;
    paint.color = _originalColor;
  }

  @override
  void update(double dt) {
    _elapsed += dt;
    if (_elapsed >= lifespan) {
      removeFromParent();
      return;
    }

    // Apply movement and gravity - dust falls down
    velocity.y += 80 * dt; // Stronger gravity for realistic falling
    position += velocity * dt;

    // Fade out gradually in the last 40% of lifespan
    final fadeStart = lifespan * 0.6;
    double alpha;
    if (_elapsed < fadeStart) {
      alpha = 1.0; // Stay fully visible
    } else {
      final fadeProgress = (_elapsed - fadeStart) / (lifespan - fadeStart);
      alpha = 1.0 - fadeProgress;
    }

    // Update color with fade using original dust color
    paint.color = _originalColor.withValues(alpha: alpha);
  }
}

class DustParticle extends PositionComponent {
  Vector2 velocity;
  double lifespan = 1.2;
  double _elapsed = 0;
  late double _size;
  late Color _color;
  final _random = math.Random();

  DustParticle({
    required Vector2 startPosition,
    required this.velocity,
  }) {
    position = startPosition;
    priority = 1000; // Very high priority to render on top of everything
    _size = _random.nextDouble() * 12 + 8; // Much bigger particles: 8-20 pixels
    _color = Color.lerp(
      Colors.cyan.withValues(alpha: 1.0), // Bright cyan
      Colors.pink.withValues(alpha: 1.0), // Bright pink
      _random.nextDouble(),
    )!;
    // Dust particle created
  }

  @override
  void update(double dt) {
    _elapsed += dt;

    if (_elapsed >= lifespan) {
      removeFromParent();
      return;
    }

    // Apply gravity and movement
    velocity.y += 100 * dt; // Moderate gravity
    position += velocity * dt;

    // Stay fully visible for most of the lifespan
    final fadeStart = lifespan * 0.8; // Start fading at 80% of lifespan
    double alpha;
    if (_elapsed < fadeStart) {
      alpha = 1.0; // Stay fully visible for first 80%
    } else {
      final fadeProgress = (_elapsed - fadeStart) / (lifespan - fadeStart);
      alpha = (1 - fadeProgress);
    }
    _color = _color.withValues(alpha: alpha);
  }

  @override
  void render(Canvas canvas) {
    final paint = Paint()..color = _color;
    canvas.drawCircle(
      Offset(position.x, position.y),
      _size,
      paint,
    );
  }
}

class ConfettiParticle extends PositionComponent {
  Vector2 velocity;
  double lifespan = 5.0; // Increased from 3.0 to 5.0 seconds
  double _elapsed = 0;
  late double _size;
  late Color _color;
  late double _rotation;
  late double _rotationSpeed;
  final _random = math.Random();

  ConfettiParticle({
    required Vector2 startPosition,
    required this.velocity,
  }) {
    position = startPosition;
    priority = 1500; // Higher than dust particles to render on top
    _size =
        _random.nextDouble() * 10 + 6; // Increased size: 6-16 pixels (was 4-10)
    _rotation = _random.nextDouble() * 6.28; // Random initial rotation
    _rotationSpeed =
        (_random.nextDouble() - 0.5) * 12; // Increased rotation speed (was 8)

    // More vibrant confetti colors with better contrast
    final colors = [
      Colors.red.shade600,
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.yellow.shade700,
      Colors.purple.shade600,
      Colors.orange.shade600,
      Colors.pink.shade400,
      Colors.cyan.shade600,
      Colors.lime.shade600,
      Colors.deepPurple.shade600,
      Colors.teal.shade600,
      Colors.amber.shade600,
    ];

    _color = colors[_random.nextInt(colors.length)];
  }

  @override
  void update(double dt) {
    _elapsed += dt;

    // Apply lighter gravity for more floating effect
    velocity.y += 250 * dt; // Reduced gravity (was 300) for more dramatic float

    // Add slight air resistance for more realistic movement
    velocity.x *= 0.998;

    // Update position
    position += velocity * dt;

    // Update rotation
    _rotation += _rotationSpeed * dt;

    // Fade out in the last 2 seconds (instead of 1) for longer visibility
    if (_elapsed > lifespan - 2.0) {
      final fadeProgress = (_elapsed - (lifespan - 2.0)) / 2.0;
      _color = _color.withValues(alpha: 1.0 - fadeProgress);
    }

    // Remove when lifetime is over or way off screen
    if (_elapsed >= lifespan || position.y > 1000) {
      removeFromParent();
    }
  }

  @override
  void render(Canvas canvas) {
    canvas.save();
    canvas.translate(position.x, position.y);
    canvas.rotate(_rotation);

    final paint = Paint()..color = _color;

    // Draw confetti as small rectangles
    final rect = Rect.fromCenter(
      center: Offset.zero,
      width: _size,
      height: _size * 0.6,
    );
    canvas.drawRect(rect, paint);

    canvas.restore();
  }
}

class ShakeComponent extends PositionComponent {
  final CameraComponent camera;
  final double duration;
  final double intensity;
  double _elapsed = 0;
  final _random = math.Random();
  final Vector2 _originalPosition;

  ShakeComponent({
    required this.camera,
    required this.duration,
    required this.intensity,
  }) : _originalPosition = camera.viewfinder.position.clone() {
    priority =
        1000; // Very high priority to ensure it updates after other components
  }

  @override
  void update(double dt) {
    _elapsed += dt;
    if (_elapsed >= duration) {
      camera.viewfinder.position = _originalPosition;
      removeFromParent();
      return;
    }

    final dx = (_random.nextDouble() * 2 - 1) * intensity;
    final dy = (_random.nextDouble() * 2 - 1) * intensity;
    camera.viewfinder.position = Vector2(
      _originalPosition.x + dx,
      _originalPosition.y + dy,
    );
  }

  @override
  void onRemove() {
    camera.viewfinder.position = _originalPosition;
    super.onRemove();
  }
}

class RunChickenRun extends FlameGame with TapDetector {
  RunChickenRun() : super(camera: CameraComponent());

  static Arena arena = Arena();
  static GameTitle gameTitle = GameTitle();
  static Marks marks = Marks();
  static GroundOne ground1 = GroundOne();
  static GroundTwo ground2 = GroundTwo();
  static ScoreBoard scoreBoard = ScoreBoard();
  static AudiencesOne audiences1 = AudiencesOne();
  static AudiencesTwo audiences2 = AudiencesTwo();
  static ChickenWalk chickenWalk = ChickenWalk(size: Vector2(44.0, 45.0));
  static ChickenReady chickenReady = ChickenReady(size: Vector2(40.0, 45.0));
  static ChickenRunOne chickenRunOne = ChickenRunOne();
  static ChickenRunTwo chickenRunTwo = ChickenRunTwo();

  static int number = 100;
  static late TextComponent oneHundred;

  // Audio management
  static bool soundEnabled = true;
  static bool backgroundMusicPlaying = false;
  static bool readySetGoPlaying = false;
  static bool audienceReactionsEnabled = true;
  static bool countingTimePlaying = false;
  static AudioPlayer? readySetGoPlayer;
  static AudioPlayer? backgroundMusicPlayer;
  static AudioPlayer? booingPlayer;
  static AudioPlayer? applausePlayer;
  static AudioPlayer? disappointedPlayer;

  static late TimerComponent runTimerStartSignal;
  static late TimerComponent runTimer;
  static late TextComponent runTimerDisplay;

  static Periods periods = Periods();
  static ReadySign readySign = ReadySign();
  static SetSign setSign = SetSign();
  static GoSign goSign = GoSign();
  static FinishSign finishSign = FinishSign();
  static FailSign failSign = FailSign();
  static FalseStart falseStart = FalseStart();

  static NewRecordAnimation newRecordAnimation =
      NewRecordAnimation(size: Vector2(375, 280));

  static StarAnimationOne starAnimationOne =
      StarAnimationOne(size: Vector2(32, 32));

  static StarAnimationTwo starAnimationTwo =
      StarAnimationTwo(size: Vector2(32, 32));

  static double scoreB4 = 8.00;

  static late double scoreNow;

  static late double scoreBest;

  static bool isNewRecord = false;

  // S-Grade continuous confetti system
  static bool isSGradeAchieved = false;
  static bool continuousConfettiActive = false;
  static late TimerComponent continuousConfettiTimer;

  static bool canTap = false;
  static bool isPeriodsAdded = false;
  static bool isReadyAdded = false;
  static bool isSetAdded = false;
  static bool isGoAdded = false;
  static bool isFinishAdded = false;
  static bool isFailAdded = false;
  static bool finished = false;
  static bool maxedOut = false;
  static bool isChickenWalkAdded = false;
  static bool isChickenReadyRemoved = false;
  static bool isChickenRunOneAdded = false;
  static bool isChickenRunTwoAdded = false;
  static bool isWalkFinished = false;
  static bool addScore = false;
  static bool isScoreBoardAdded = false;
  static bool isNewRecordAnimationAdded = false;
  static bool isStarAnimationOneAdded = false;
  static bool isStarAnimationTwoAdded = false;
  static bool isBestDataElementsAdded = false;
  static bool isGradeNowDataElementsAdded = false;
  static bool isGradeBestDataElementsAdded = false;
  static bool canRun = false;

  // TAP TAP instruction variables
  static bool isFirstTime = true;
  static bool isTapInstructionAdded = false;
  static late TextComponent tapInstructionStroke;
  static late TextComponent tapInstructionFill;
  static late TimerComponent tapInstructionTimer;
  static double tapInstructionOpacity = 1.0;
  static bool tapInstructionFading = false;
  static double tapInstructionScale = 1.0;
  static double tapInstructionPulseTime = 0.0;
  static bool tapInstructionPulsing = false;

  // Persistent TAP! TAP! reminder system for player engagement
  static late TimerComponent persistentTapReminderTimer;
  static bool persistentReminderActive = false;

  static Score scoreCount = Score(game: RunChickenRun());
  static Finish finish = Finish(game: RunChickenRun());

  bool _isShaking = false;
  double _shakeTime = 0;
  double _shakeDuration = 0;
  double _shakeIntensity = 0;
  Vector2 _originalPosition = Vector2.zero();
  final _random = math.Random();

  // Screen flash variables for new record
  bool _isFlashing = false;
  double _flashTime = 0;
  double _flashDuration = 0.3; // Longer flash duration
  double _flashOpacity = 0;

  late ScoreManager _scoreManager;

  // Getter for accessing score manager from overlays
  ScoreManager get scoreManager => _scoreManager;

  @override
  Future<void> onLoad() async {
    camera.viewfinder.position = Vector2.zero();
    camera.viewfinder.anchor = Anchor.center;
    camera.viewfinder.zoom = 1.0;

    // Optimize rendering for pixel art
    camera.viewfinder.visibleGameSize = canvasSize;

    // Check if this is the first time playing
    await _checkFirstTime();

    _scoreManager = ScoreManager();
    _scoreManager.position =
        Vector2(canvasSize.x, 0); // Position to allow both corners
    await add(_scoreManager);

    runTimerStartSignal = TimerComponent(
      period: 1.20,
      onTick: () {
        runTimer.timer.start();
      },
      autoStart: false,
      repeat: false,
    );

    runTimer = TimerComponent(
      period: 8.00,
      autoStart: false,
      repeat: false,
    );

    // Load images with optimized settings for pixel art
    await images.loadAll([
      "audiences_1.png",
      "audiences_2.png",
      "chicken_ready.png",
      "chicken_run_1.png",
      "chicken_run_2.png",
      "chicken_walk.png",
      "fail.png",
      "false_start.png",
      "finished.png",
      "glanced.png",
      "go.png",
      "ground_1.png",
      "ground_2.png",
      "mark.png",
      "new_record_animation.png",
      "periods.png",
      "ready.png",
      "scoreboard.png",
      "set.png",
      "star_animation.png",
      "title.png",
      "world.png"
    ]);

    await add(arena);

    await add(gameTitle);
    gameTitle.position = Vector2(canvasSize.x / 2, (canvasSize.y / 2) - 135);

    await add(audiences1);
    audiences1.position = Vector2(0, canvasSize.y - 270);
    audiences2.position = Vector2(0, canvasSize.y - 270);

    await add(ground1);
    ground1.position = Vector2(0, canvasSize.y - 223);
    ground2.position = Vector2(0, canvasSize.y - 223);
    marks.position = Vector2((canvasSize.x / 2) + 3, canvasSize.y - 203);

    chickenWalk.position = Vector2(canvasSize.x / 2, canvasSize.y - 235);
    await add(chickenWalk);

    chickenReady.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);

    chickenRunOne.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);

    periods.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 250);
    readySign.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 280);
    setSign.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 280);
    goSign.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 280);
    finishSign.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 280);
    failSign.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 280);
    falseStart.position = Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 200);

    scoreBoard.position =
        Vector2((canvasSize.x / 2), ((canvasSize.y / 2) - 25));

    newRecordAnimation.position =
        Vector2((canvasSize.x / 2), ((canvasSize.y) / 2) - 82.5);

    starAnimationOne.position =
        Vector2((canvasSize.x / 2) - 120, (canvasSize.y / 2) + 18.5);

    starAnimationTwo.position =
        Vector2((canvasSize.x / 2) + 120, (canvasSize.y / 2) + 18.5);

    oneHundred = TextComponent(
      text: number.toString(),
      anchor: Anchor.center,
      position: Vector2(
        canvasSize.x / 2,
        (canvasSize.y / 2) - 75,
      ),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 200,
          color: Colors.black54,
          fontFamily: '04B_19__',
        ),
      ),
    );

    runTimerDisplay = TextComponent(
      text: runTimer.timer.current.toStringAsPrecision(2),
      anchor: Anchor.center,
      position: Vector2(
        canvasSize.x / 2,
        canvasSize.y - 85,
      ),
      priority: 3,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 53,
          fontWeight: FontWeight.w900,
          color: Colors.amberAccent,
          fontFamily: 'Digital',
        ),
      ),
    );

    // Setup TAP TAP instruction with stroke outline like scoreNow
    tapInstructionStroke = TextComponent(
      text: 'TAP! TAP!',
      anchor: Anchor.center,
      position: Vector2(
        canvasSize.x / 2,
        canvasSize.y / 2 + 50,
      ),
      priority: 1000,
      textRenderer: TextPaint(
        style: TextStyle(
          fontSize: 24,
          fontFamily: '04B_19__',
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = 4
            ..color = Colors.black87,
        ),
      ),
    );

    tapInstructionFill = TextComponent(
      text: 'TAP! TAP!',
      anchor: Anchor.center,
      position: Vector2(
        canvasSize.x / 2,
        canvasSize.y / 2 + 50,
      ),
      priority: 1001,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 24,
          fontFamily: '04B_19__',
          color: Colors.white,
        ),
      ),
    );

    // Timer to start fade out after 1.5 seconds
    tapInstructionTimer = TimerComponent(
      period: 1.5,
      onTick: () {
        if (isTapInstructionAdded) {
          tapInstructionFading = true;
        }
      },
      autoStart: false,
      repeat: false,
    );

    // Persistent reminder timer - shows TAP! TAP! every 2.5 seconds if player isn't tapping
    persistentTapReminderTimer = TimerComponent(
      period: 2.5, // Every 2.5 seconds
      onTick: () {
        if (canTap && number == 100 && !persistentReminderActive) {
          // Player can tap but hasn't started yet - show urging TAP! TAP!
          showPersistentTapReminder();
        }
      },
      autoStart: false,
      repeat: true, // Keep repeating every 2.5 seconds
    );

    // Continuous confetti timer for S-grade achievements
    continuousConfettiTimer = TimerComponent(
      period: 0.8, // Every 0.8 seconds for dramatic continuous effect
      onTick: () {
        if (continuousConfettiActive) {
          createSGradeConfettiBurst();
        }
      },
      autoStart: false,
      repeat: true,
    );

    // Start background music
    await playBackgroundMusic();
  }

  Future<void> _checkFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    isFirstTime = prefs.getBool('first_time') ?? true;
  }

  static Future<void> markNotFirstTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('first_time', false);
    isFirstTime = false;
  }

  // Show persistent TAP! TAP! reminder with vibration for player engagement
  void showPersistentTapReminder() {
    persistentReminderActive = true;

    // Add TAP! TAP! instruction if not already visible
    if (!isTapInstructionAdded) {
      add(tapInstructionStroke);
      add(tapInstructionFill);
      isTapInstructionAdded = true;
    }

    // Reset opacity and stop any existing fade
    tapInstructionFading = false;
    tapInstructionOpacity = 1.0;

    // Ensure stroke is visible with proper styling
    tapInstructionStroke.textRenderer = TextPaint(
      style: TextStyle(
        fontSize: 24,
        fontFamily: '04B_19__',
        foreground: Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 4
          ..color = Colors.black87,
      ),
    );

    // Ensure fill is visible with proper styling
    tapInstructionFill.textRenderer = TextPaint(
      style: const TextStyle(
        fontSize: 24,
        fontFamily: '04B_19__',
        color: Colors.white,
      ),
    );

    // Start fade out timer
    tapInstructionTimer.timer.stop();
    tapInstructionTimer.timer.start();

    // Trigger vibration for attention
    Vibration.vibrate(duration: 200, amplitude: 255);

    // Reset reminder flag after a short delay to allow next reminder
    Future.delayed(const Duration(milliseconds: 500), () {
      persistentReminderActive = false;
    });
  }

  // Instantly hide TAP! TAP! instruction
  void hideTapInstructionInstantly() {
    if (isTapInstructionAdded) {
      remove(tapInstructionStroke);
      remove(tapInstructionFill);
      isTapInstructionAdded = false;
      tapInstructionFading = false;
      tapInstructionOpacity = 1.0;
      tapInstructionScale = 1.0;
      tapInstructionPulseTime = 0.0;
      tapInstructionTimer.timer.stop();
      persistentTapReminderTimer.timer.stop();
    }
  }

  // Create dramatic confetti burst for new records
  void createConfettiBurst() {
    final random = math.Random();
    final centerX = canvasSize.x / 2;
    final centerY = canvasSize.y / 2;

    // Create MANY more confetti particles for dramatic effect
    for (int i = 0; i < 100; i++) {
      // Doubled from 50 to 100 particles
      // Wider spread around the center for more dramatic effect
      final startX =
          centerX + (random.nextDouble() - 0.5) * 300; // Increased spread
      final startY =
          centerY + (random.nextDouble() - 0.5) * 150; // Increased spread

      // More dramatic velocity - stronger upward burst
      final velocityX = (random.nextDouble() - 0.5) * 600; // Increased from 400
      final velocityY = -300 -
          random.nextDouble() * 400; // Much stronger upward (was -200 to -500)

      final confetti = ConfettiParticle(
        startPosition: Vector2(startX, startY),
        velocity: Vector2(velocityX, velocityY),
      );

      add(confetti);
    }

    // Add a second wave of confetti for extra drama
    Future.delayed(const Duration(milliseconds: 200), () {
      for (int i = 0; i < 50; i++) {
        final startX = centerX + (random.nextDouble() - 0.5) * 250;
        final startY = centerY + (random.nextDouble() - 0.5) * 120;

        final velocityX = (random.nextDouble() - 0.5) * 500;
        final velocityY = -250 - random.nextDouble() * 350;

        final confetti = ConfettiParticle(
          startPosition: Vector2(startX, startY),
          velocity: Vector2(velocityX, velocityY),
        );

        add(confetti);
      }
    });
  }

  // Create S-grade confetti burst - more dramatic and colorful
  void createSGradeConfettiBurst() {
    final random = math.Random();
    final centerX = canvasSize.x / 2;
    final centerY = canvasSize.y / 2;

    // Create fewer but more dramatic particles for continuous effect
    for (int i = 0; i < 30; i++) {
      // Random position around the center with wider spread
      final startX = centerX + (random.nextDouble() - 0.5) * 350;
      final startY = centerY + (random.nextDouble() - 0.5) * 200;

      // More dramatic velocity for S-grade celebration
      final velocityX = (random.nextDouble() - 0.5) * 500;
      final velocityY = -250 - random.nextDouble() * 350;

      final confetti = ConfettiParticle(
        startPosition: Vector2(startX, startY),
        velocity: Vector2(velocityX, velocityY),
      );

      add(confetti);
    }
  }

  // Start continuous confetti for S-grade
  void startContinuousConfetti() {
    continuousConfettiActive = true;
    continuousConfettiTimer.timer.start();
    // Create initial burst immediately
    createSGradeConfettiBurst();
  }

  // Stop continuous confetti
  void stopContinuousConfetti() {
    continuousConfettiActive = false;
    continuousConfettiTimer.timer.stop();
  }

  // Audio helper methods
  static Future<void> playSound(String soundPath) async {
    if (soundEnabled) {
      try {
        await FlameAudio.play(soundPath);
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static Future<void> playBackgroundMusic() async {
    if (soundEnabled && !backgroundMusicPlaying) {
      try {
        backgroundMusicPlayer = AudioPlayer();
        await backgroundMusicPlayer!.setReleaseMode(ReleaseMode.loop);
        await backgroundMusicPlayer!.setVolume(0.6);
        await backgroundMusicPlayer!
            .play(AssetSource('audio/run_chicken_run_bgm.mp3'));
        backgroundMusicPlaying = true;
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static Future<void> stopBackgroundMusic() async {
    if (backgroundMusicPlaying) {
      try {
        if (backgroundMusicPlayer != null) {
          await backgroundMusicPlayer!.stop();
          await backgroundMusicPlayer!.dispose();
          backgroundMusicPlayer = null;
        }
        backgroundMusicPlaying = false;
      } catch (e) {
        // Debug print removed
      }
    }
  }

  static Future<void> playReadySound() async {
    if (soundEnabled && readySetGoPlaying) {
      try {
        // Use non-awaited play for immediate response
        FlameAudio.play('ready.mp3');
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static Future<void> playSetSound() async {
    if (soundEnabled && readySetGoPlaying) {
      try {
        await FlameAudio.play('set.mp3');
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static Future<void> playGoSound() async {
    if (soundEnabled && readySetGoPlaying) {
      try {
        await FlameAudio.play('go.mp3');
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static void startReadySetGoSequence() {
    readySetGoPlaying = true;
  }

  static void stopReadySetGoSequence() {
    readySetGoPlaying = false;
  }

  // Audience reaction sounds
  static Future<void> playApplause() async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        await FlameAudio.play('applause.ogg');
      } catch (e) {
        // Handle error silently
      }
    }
  }

  static Future<void> playApplauseWithCallback(Function onComplete) async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        // Debug print removed
        applausePlayer = AudioPlayer();

        // Listen for when the sound completes
        applausePlayer!.onPlayerComplete.listen((_) {
          // Debug print removed
          onComplete();
        });

        await applausePlayer!.play(AssetSource('audio/applause.ogg'));
      } catch (e) {
        // Debug print removed
        // Call callback even if there's an error to prevent hanging
        onComplete();
      }
    } else {
      // If sound is disabled, call callback immediately
      onComplete();
    }
  }

  static Future<void> playBooing() async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        // Debug print removed
        booingPlayer = AudioPlayer();
        await booingPlayer!.play(AssetSource('audio/booing.ogg'));
      } catch (e) {
        // Debug print removed
      }
    }
  }

  static Future<void> playBooingWithCallback(Function onComplete) async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        // Debug print removed
        booingPlayer = AudioPlayer();

        // Listen for when the sound completes
        booingPlayer!.onPlayerComplete.listen((_) {
          // Debug print removed
          onComplete();
        });

        await booingPlayer!.play(AssetSource('audio/booing.ogg'));
      } catch (e) {
        // Debug print removed
        // Call callback even if there's an error to prevent hanging
        onComplete();
      }
    } else {
      // If sound is disabled, call callback immediately
      onComplete();
    }
  }

  static Future<void> stopBooing() async {
    try {
      if (booingPlayer != null) {
        await booingPlayer!.stop();
        await booingPlayer!.dispose();
        booingPlayer = null;
        // Debug print removed
      }
    } catch (e) {
      // Debug print removed
    }
  }

  static Future<void> stopApplause() async {
    try {
      if (applausePlayer != null) {
        await applausePlayer!.stop();
        await applausePlayer!.dispose();
        applausePlayer = null;
        // Debug print removed
      }
    } catch (e) {
      // Debug print removed
    }
  }

  static Future<void> stopDisappointed() async {
    try {
      if (disappointedPlayer != null) {
        await disappointedPlayer!.stop();
        await disappointedPlayer!.dispose();
        disappointedPlayer = null;
        // Debug print removed
      }
    } catch (e) {
      // Debug print removed
    }
  }

  static Future<void> playDisappointed() async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        // Debug print removed
        await FlameAudio.play('disappointed.ogg');
      } catch (e) {
        // Debug print removed
      }
    }
  }

  static Future<void> playDisappointedWithCallback(Function onComplete) async {
    if (soundEnabled && audienceReactionsEnabled) {
      try {
        // Debug print removed
        disappointedPlayer = AudioPlayer();

        // Listen for when the sound completes
        disappointedPlayer!.onPlayerComplete.listen((_) {
          // Debug print removed
          onComplete();
        });

        await disappointedPlayer!.play(AssetSource('audio/disappointed.ogg'));
      } catch (e) {
        // Debug print removed
        // Call callback even if there's an error to prevent hanging
        onComplete();
      }
    } else {
      // If sound is disabled, call callback immediately
      onComplete();
    }
  }

  static Future<void> playCameraShutter() async {
    if (soundEnabled) {
      try {
        // Debug print removed
        await FlameAudio.play('camera_shutter.mp3');
      } catch (e) {
        // Debug print removed
      }
    }
  }

  static void startRapidCountingTime() {
    if (soundEnabled) {
      countingTimePlaying = true;
      // Debug print removed
      _playCountingTimeRapidly();
    }
  }

  static void stopRapidCountingTime() {
    countingTimePlaying = false;
    // Debug print removed
  }

  static void _playCountingTimeRapidly() async {
    if (!countingTimePlaying) return;

    try {
      // Play the beep sound for counting effect
      FlameAudio.play('one_beep.mp3');

      // Schedule next play after a short interval (rapid fire)
      Future.delayed(const Duration(milliseconds: 80), () {
        _playCountingTimeRapidly();
      });
    } catch (e) {
      // Debug print removed
    }
  }

  static Future<void> stopAllSounds() async {
    try {
      // Stop ready-set-go audio immediately
      if (readySetGoPlayer != null) {
        await readySetGoPlayer!.stop();
        await readySetGoPlayer!.dispose();
        readySetGoPlayer = null;
      }
      readySetGoPlaying = false;

      // Disable audience reactions to prevent new ones from playing
      audienceReactionsEnabled = false;

      // Stop rapid counting time sound
      stopRapidCountingTime();

      // Stop audience reaction sounds
      await stopBooing();
      await stopApplause();
      await stopDisappointed();

      // Stop background music
      await stopBackgroundMusic();

      // Stop all currently playing sounds and clear cache
      try {
        await FlameAudio.bgm.stop(); // This might stop all FlameAudio sounds
      } catch (e) {
        // Ignore if no bgm is playing
      }
      FlameAudio.audioCache.clearAll();
    } catch (e) {
      // Handle error silently
    }
  }

  void shakeScreen({double duration = 0.8, double intensity = 40}) {
    _isShaking = true;
    _shakeTime = 0;
    _shakeDuration = duration;
    _shakeIntensity = intensity;
    _originalPosition = Vector2.zero();
  }

  void flashScreen({double duration = 0.3}) {
    _isFlashing = true;
    _flashTime = 0;
    _flashDuration = duration;
    _flashOpacity = 1.0; // Start with full white flash
  }

  void createDustParticles() {
    // Dust particles removed - user preference
  }

  @override
  void render(Canvas canvas) {
    // Enable pixel-perfect rendering for crisp pixel art
    canvas.save();

    super.render(canvas);

    // Render screen flash for new record
    if (_isFlashing && _flashOpacity > 0) {
      final flashPaint = Paint()
        ..color = Colors.white.withValues(alpha: _flashOpacity)
        ..style = PaintingStyle.fill;

      canvas.drawRect(
        Rect.fromLTWH(0, 0, canvasSize.x, canvasSize.y),
        flashPaint,
      );
    }

    canvas.restore();
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (ground1.position.x <= -510) {
      ground1.position.x = 0;
    }

    if (audiences1.position.x <= -500) {
      audiences1.position.x = 0;
    }

    runTimerStartSignal.timer.update(dt);
    runTimer.timer.update(dt);
    tapInstructionTimer.timer.update(dt);
    persistentTapReminderTimer.timer.update(dt);
    runTimerDisplay.text = runTimer.timer.current.toStringAsPrecision(3);
    oneHundred.text = number.toString();

    // Update timer color based on remaining time (only during active gameplay)
    if (runTimer.timer.isRunning() && canRun && !finished) {
      final remainingTime = runTimer.timer.limit - runTimer.timer.current;
      Color timerColor;

      if (remainingTime > 3.0) {
        // Normal time - amber
        timerColor = Colors.amberAccent;
      } else if (remainingTime > 1.5) {
        // Getting critical - orange
        timerColor = Colors.orange;
      } else if (remainingTime > 0.5) {
        // Very critical - red
        timerColor = Colors.red;
      } else {
        // Almost out of time - bright red with pulsing effect
        final pulseIntensity = (math.sin(runTimer.timer.current * 20) + 1) / 2;
        timerColor = Color.lerp(Colors.red, Colors.redAccent, pulseIntensity)!;
      }

      // Update timer display color and size for urgency
      double fontSize = 53;
      if (remainingTime <= 1.5) {
        // Make timer larger when critical
        fontSize = remainingTime <= 0.5 ? 58 : 55;
      }

      runTimerDisplay.textRenderer = TextPaint(
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w900,
          color: timerColor,
          fontFamily: 'Digital',
        ),
      );
    } else {
      // Reset to default amber color when not in active gameplay
      runTimerDisplay.textRenderer = TextPaint(
        style: const TextStyle(
          fontSize: 53,
          fontWeight: FontWeight.w900,
          color: Colors.amberAccent,
          fontFamily: 'Digital',
        ),
      );
    }

    // Handle TAP! TAP! urgent pulsing animation
    if (isTapInstructionAdded && !tapInstructionFading) {
      tapInstructionPulsing = true;
      tapInstructionPulseTime +=
          dt * 10.0; // Much faster pulsing for maximum urgency

      // Create urgent pulsing effect: scale between 1.0 and 1.3
      tapInstructionScale =
          1.0 + (math.sin(tapInstructionPulseTime) * 0.15).abs();

      // Apply scale to both stroke and fill
      tapInstructionStroke.scale = Vector2.all(tapInstructionScale);
      tapInstructionFill.scale = Vector2.all(tapInstructionScale);

      // Add color pulsing for extra urgency - flash between white and red
      final colorIntensity = (math.sin(tapInstructionPulseTime * 2.0) + 1) / 2;
      final pulseColor = Color.lerp(Colors.white, Colors.red, colorIntensity)!;

      tapInstructionFill.textRenderer = TextPaint(
        style: TextStyle(
          fontSize: 24,
          color: pulseColor,
          fontFamily: '04B_19__',
        ),
      );
    }

    // Handle TAP! TAP! fade out
    if (tapInstructionFading && isTapInstructionAdded) {
      tapInstructionPulsing = false;
      tapInstructionOpacity -= dt * 2.0; // Fade out over 0.5 seconds
      if (tapInstructionOpacity <= 0) {
        tapInstructionOpacity = 0;
        remove(tapInstructionStroke);
        remove(tapInstructionFill);
        isTapInstructionAdded = false;
        tapInstructionFading = false;
        tapInstructionOpacity = 1.0; // Reset for next time
        tapInstructionScale = 1.0; // Reset scale
        tapInstructionPulseTime = 0.0; // Reset pulse time
      } else {
        // Update opacity by modifying text color alpha
        final strokeColor =
            Colors.black87.withValues(alpha: tapInstructionOpacity);
        final fillColor = Colors.white.withValues(alpha: tapInstructionOpacity);

        tapInstructionStroke.textRenderer = TextPaint(
          style: TextStyle(
            fontSize: 24,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 4
              ..color = strokeColor,
          ),
        );

        tapInstructionFill.textRenderer = TextPaint(
          style: TextStyle(
            fontSize: 24,
            fontFamily: '04B_19__',
            color: fillColor,
          ),
        );
      }
    }

    // Handle screen flash for new record
    if (_isFlashing) {
      _flashTime += dt;
      if (_flashTime >= _flashDuration) {
        _isFlashing = false;
        _flashOpacity = 0;
      } else {
        // Fade out the flash over time
        final progress = _flashTime / _flashDuration;
        _flashOpacity = 1.0 * (1 - progress); // Fade from 1.0 to 0
      }
    }

    if (_isShaking) {
      _shakeTime += dt;
      if (_shakeTime >= _shakeDuration) {
        _isShaking = false;
        arena.position = _originalPosition;
        gameTitle.position =
            Vector2(canvasSize.x / 2, (canvasSize.y / 2) - 135);
        audiences1.position = Vector2(0, canvasSize.y - 270);
        audiences2.position = Vector2(0, canvasSize.y - 270);
        ground1.position = Vector2(0, canvasSize.y - 223);
        ground2.position = Vector2(0, canvasSize.y - 223);
        marks.position = Vector2((canvasSize.x / 2) + 3, canvasSize.y - 203);
        falseStart.position =
            Vector2(canvasSize.x / 2, ((canvasSize.y) / 2) - 200);
        chickenReady.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);
        chickenWalk.position = Vector2(canvasSize.x / 2, canvasSize.y - 235);
        chickenRunOne.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);
        chickenRunTwo.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);
      } else {
        final progress = _shakeTime / _shakeDuration;
        final currentIntensity = _shakeIntensity * (1 - progress);

        final dx1 = (_random.nextDouble() * 2 - 1) * currentIntensity;
        final dy1 = (_random.nextDouble() * 2 - 1) * currentIntensity;
        final dx2 = (_random.nextDouble() * 2 - 1) * currentIntensity * 0.5;
        final dy2 = (_random.nextDouble() * 2 - 1) * currentIntensity * 0.5;

        final dx = dx1 + dx2;
        final dy = dy1 + dy2;

        arena.position = Vector2(dx, dy);
        gameTitle.position =
            Vector2(canvasSize.x / 2 + dx, (canvasSize.y / 2) - 135 + dy);
        audiences1.position = Vector2(dx, canvasSize.y - 270 + dy);
        audiences2.position = Vector2(dx, canvasSize.y - 270 + dy);
        ground1.position = Vector2(dx, canvasSize.y - 223 + dy);
        ground2.position = Vector2(dx, canvasSize.y - 223 + dy);
        marks.position =
            Vector2((canvasSize.x / 2) + 3 + dx, canvasSize.y - 203 + dy);
        falseStart.position =
            Vector2(canvasSize.x / 2 + dx, ((canvasSize.y) / 2) - 200 + dy);
        chickenReady.position =
            Vector2(canvasSize.x / 2 + dx, canvasSize.y - 234 + dy);
        chickenWalk.position =
            Vector2(canvasSize.x / 2 + dx, canvasSize.y - 235 + dy);
        chickenRunOne.position =
            Vector2(canvasSize.x / 2 + dx, canvasSize.y - 234 + dy);
        chickenRunTwo.position =
            Vector2(canvasSize.x / 2 + dx, canvasSize.y - 234 + dy);
      }
    }

    if (number == 0 && finished == false) {
      canTap = false;
      finished = true;
      canRun = false;
      scoreNow = runTimer.timer.current;
      _scoreManager.saveScore(scoreNow);
      runTimer.timer.stop();
      Vibration.cancel();
      add(finishSign);
      remove(goSign);
      remove(oneHundred);
      remove(runTimerDisplay);

      // Play finish line reached sound
      playSound('finish_line_reached.mp3');
      // Audience reaction will be determined in scoreboard based on performance
      if (isChickenRunOneAdded) {
        remove(chickenRunOne);
      } else if (isChickenRunTwoAdded) {
        remove(chickenRunTwo);
      }
      if (isChickenWalkAdded == false) {
        add(chickenWalk);
      }

      if (_scoreManager.isNewRecord(scoreNow)) {
        isNewRecord = true;
        add(newRecordAnimation);
        // Create confetti burst for new record celebration
        createConfettiBurst();
        // Flash will be triggered after score animation completes in ScoreBoard
      } else {
        isNewRecord = false;
      }

      // Check if S-grade is achieved (score < 6.00)
      if (scoreNow < 6.00) {
        isSGradeAchieved = true;
        // Start continuous confetti for S-grade achievement
        startContinuousConfetti();
      } else {
        isSGradeAchieved = false;
      }

      add(scoreBoard);
    } else if (runTimer.timer.finished && number <= 99 && maxedOut == false) {
      canTap = false;
      finished = true;
      maxedOut = true;
      canRun = false;
      scoreNow = 8.00;
      isNewRecord = false; // Timeout is never a new record
      _scoreManager.saveScore(scoreNow);
      runTimer.timer.stop();
      Vibration.cancel();
      // Hide TAP! TAP! instruction instantly when time runs out
      hideTapInstructionInstantly();
      if (isGoAdded) {
        remove(goSign);
      }
      remove(oneHundred);
      remove(runTimerDisplay);
      add(failSign);
      isFailAdded = true;
      add(scoreBoard);

      // Play maxed out sound
      playSound('maxed_out.wav');
      // Booing will be played after score animation finishes
      if (isChickenRunOneAdded) {
        remove(chickenRunOne);
      }
      if (isChickenRunTwoAdded) {
        remove(chickenRunTwo);
      }
      add(chickenWalk);
    } else if (runTimer.timer.finished && number > 99 && maxedOut == false) {
      canTap = false;
      finished = true;
      maxedOut = true;
      canRun = false;
      scoreNow = 8.00;
      isNewRecord = false; // Timeout is never a new record
      _scoreManager.saveScore(scoreNow);
      runTimer.timer.stop();
      Vibration.cancel();
      // Hide TAP! TAP! instruction instantly when time runs out
      hideTapInstructionInstantly();
      if (isGoAdded) {
        remove(goSign);
      }
      remove(oneHundred);
      remove(runTimerDisplay);
      add(failSign);
      add(scoreBoard);

      // Play maxed out sound
      playSound('maxed_out.wav');
      // Booing will be played after score animation finishes
      if (isChickenRunOneAdded) {
        remove(chickenRunOne);
      } else if (isChickenRunTwoAdded) {
        remove(chickenRunTwo);
      }
      remove(chickenReady);
      add(chickenWalk);
    }

    if (isWalkFinished && isChickenReadyRemoved && finished) {
      chickenReady.position =
          Vector2((canvasSize.x / 2) + 50, canvasSize.y - 234);
      add(chickenReady);
    }
  }

  @override
  void onTapDown(TapDownInfo info) {
    // Handle game tap events
    if (canRun &&
        canTap &&
        runTimerStartSignal.timer.finished &&
        finished == false &&
        maxedOut == false) {
      if (isChickenReadyRemoved == false) {
        remove(chickenReady);
      }

      // Create dust particles when chicken first starts running
      if (number == 100) {
        createDustParticles();
        // Hide TAP! TAP! instruction instantly when player starts tapping
        hideTapInstructionInstantly();
      }

      number -= 1;
      audiences2.position += audiences2.velocity;
      ground2.position += ground2.velocity;
      marks.position += marks.velocity;

      // Play tap sound
      playSound('tap.wav');

      // Ensure we remove both chickens first
      if (children.contains(chickenRunOne)) {
        remove(chickenRunOne);
      }
      if (children.contains(chickenRunTwo)) {
        remove(chickenRunTwo);
      }

      // Then add the appropriate chicken with correct position
      if (isChickenRunOneAdded) {
        chickenRunTwo.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);
        add(chickenRunTwo);
      } else {
        chickenRunOne.position = Vector2(canvasSize.x / 2, canvasSize.y - 234);
        add(chickenRunOne);
      }

      Vibration.vibrate(duration: 50, amplitude: 255);
    } else if (canRun && !runTimer.timer.isRunning()) {
      runTimerStartSignal.timer.stop();
      canTap = false;
      canRun = false;
      if (isPeriodsAdded) {
        remove(periods);
      }
      if (isReadyAdded) {
        remove(readySign);
      }
      if (isSetAdded) {
        remove(setSign);
      }
      if (isGoAdded) {
        remove(goSign);
      }
      remove(oneHundred);
      remove(runTimerDisplay);

      // Stop ready-set-go audio immediately when false start occurs
      stopReadySetGoSequence();
      stopAllSounds();

      shakeScreen(
          duration: 0.6, intensity: 60); // More subtle shake for false start
      Future.delayed(const Duration(milliseconds: 100)).then((_) {
        Vibration.vibrate(duration: 500, amplitude: 255);
        add(falseStart);
        // Play false start sound
        playSound('false_start.wav');
        Future.delayed(const Duration(milliseconds: 1100)).then((_) {
          overlays.add('Finish_False');
          // Play booing first after false start overlay appears
          Future.delayed(const Duration(milliseconds: 200), () {
            // Re-enable audience reactions specifically for booing
            audienceReactionsEnabled = true;

            playBooingWithCallback(() {
              // Play background music 50ms after booing finishes
              Future.delayed(const Duration(milliseconds: 50), () {
                playBackgroundMusic();
              });
            });
          });
        });
      });
    }
  }
}
