import 'package:flame/sprite.dart';

extension CreateAnimationByLimit on SpriteSheet {
  SpriteAnimation createAnimationByLimit({
    required int xInit,
    required int yInit,
    required int step,
    required int sizeX,
    required double stepTime,
    bool loop = false,
  }) {
    final List<Sprite> spriteList = [];

    int x = xInit;
    int y = yInit;

    for (var i = 0; i < step; i++) {
      if (y <= sizeX) {
        x = 0;
        y++;
      } else {
        x = 0;
        y = -1;
      }

      spriteList.add(getSprite(x, y));
      // print(x.toString() + '' + y.toStirng());
    }

    return SpriteAnimation.spriteList(spriteList,
        stepTime: stepTime, loop: loop);
  }
}
